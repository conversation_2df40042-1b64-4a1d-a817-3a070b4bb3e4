# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/python/keras/protobuf/saved_metadata.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.python.keras.protobuf import versions_pb2 as tensorflow_dot_python_dot_keras_dot_protobuf_dot_versions__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/python/keras/protobuf/saved_metadata.proto',
  package='third_party.tensorflow.python.keras.protobuf',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n5tensorflow/python/keras/protobuf/saved_metadata.proto\x12,third_party.tensorflow.python.keras.protobuf\x1a/tensorflow/python/keras/protobuf/versions.proto\"Y\n\rSavedMetadata\x12H\n\x05nodes\x18\x01 \x03(\x0b\x32\x39.third_party.tensorflow.python.keras.protobuf.SavedObject\"\xa8\x01\n\x0bSavedObject\x12\x0f\n\x07node_id\x18\x02 \x01(\x05\x12\x11\n\tnode_path\x18\x03 \x01(\t\x12\x12\n\nidentifier\x18\x04 \x01(\t\x12\x10\n\x08metadata\x18\x05 \x01(\t\x12I\n\x07version\x18\x06 \x01(\x0b\x32\x38.third_party.tensorflow.python.keras.protobuf.VersionDefJ\x04\x08\x01\x10\x02\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_python_dot_keras_dot_protobuf_dot_versions__pb2.DESCRIPTOR,])




_SAVEDMETADATA = _descriptor.Descriptor(
  name='SavedMetadata',
  full_name='third_party.tensorflow.python.keras.protobuf.SavedMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='nodes', full_name='third_party.tensorflow.python.keras.protobuf.SavedMetadata.nodes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=152,
  serialized_end=241,
)


_SAVEDOBJECT = _descriptor.Descriptor(
  name='SavedObject',
  full_name='third_party.tensorflow.python.keras.protobuf.SavedObject',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='node_id', full_name='third_party.tensorflow.python.keras.protobuf.SavedObject.node_id', index=0,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='node_path', full_name='third_party.tensorflow.python.keras.protobuf.SavedObject.node_path', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='identifier', full_name='third_party.tensorflow.python.keras.protobuf.SavedObject.identifier', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='third_party.tensorflow.python.keras.protobuf.SavedObject.metadata', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='version', full_name='third_party.tensorflow.python.keras.protobuf.SavedObject.version', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=244,
  serialized_end=412,
)

_SAVEDMETADATA.fields_by_name['nodes'].message_type = _SAVEDOBJECT
_SAVEDOBJECT.fields_by_name['version'].message_type = tensorflow_dot_python_dot_keras_dot_protobuf_dot_versions__pb2._VERSIONDEF
DESCRIPTOR.message_types_by_name['SavedMetadata'] = _SAVEDMETADATA
DESCRIPTOR.message_types_by_name['SavedObject'] = _SAVEDOBJECT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SavedMetadata = _reflection.GeneratedProtocolMessageType('SavedMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SAVEDMETADATA,
  '__module__' : 'tensorflow.python.keras.protobuf.saved_metadata_pb2'
  # @@protoc_insertion_point(class_scope:third_party.tensorflow.python.keras.protobuf.SavedMetadata)
  })
_sym_db.RegisterMessage(SavedMetadata)

SavedObject = _reflection.GeneratedProtocolMessageType('SavedObject', (_message.Message,), {
  'DESCRIPTOR' : _SAVEDOBJECT,
  '__module__' : 'tensorflow.python.keras.protobuf.saved_metadata_pb2'
  # @@protoc_insertion_point(class_scope:third_party.tensorflow.python.keras.protobuf.SavedObject)
  })
_sym_db.RegisterMessage(SavedObject)


# @@protoc_insertion_point(module_scope)
