# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/python/tpu/tensor_tracer.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import graph_pb2 as tensorflow_dot_core_dot_framework_dot_graph__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/python/tpu/tensor_tracer.proto',
  package='tensorflow',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n)tensorflow/python/tpu/tensor_tracer.proto\x12\ntensorflow\x1a%tensorflow/core/framework/graph.proto\"\xb4\t\n\x12TensorTracerReport\x12\x41\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x31.tensorflow.TensorTracerReport.TensorTracerConfig\x12&\n\x08graphdef\x18\x02 \x01(\x0b\x32\x14.tensorflow.GraphDef\x12@\n\ttensordef\x18\x03 \x03(\x0b\x32-.tensorflow.TensorTracerReport.TensordefEntry\x12\x13\n\x0b\x66ingerprint\x18\x04 \x01(\t\x12\x1e\n\x16\x63oncrete_function_name\x18\x05 \x01(\t\x12\x1c\n\x14last_common_frame_no\x18\x06 \x01(\x05\x12\x0f\n\x07outputs\x18\x07 \x03(\t\x12\x42\n\rtracing_stats\x18\x08 \x01(\x0b\x32+.tensorflow.TensorTracerReport.TracingStats\x1a`\n\x0eTensordefEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12=\n\x05value\x18\x02 \x01(\x0b\x32..tensorflow.TensorTracerReport.TracedTensorDef:\x02\x38\x01\x1a\xc8\x01\n\x12TensorTracerConfig\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65vice\x18\x02 \x01(\t\x12\x12\n\ntrace_mode\x18\x03 \x01(\t\x12\x11\n\tnum_cores\x18\x04 \x01(\x05\x12\x11\n\tnum_hosts\x18\x05 \x01(\x05\x12\x0f\n\x07submode\x18\x06 \x01(\t\x12\x1a\n\x12num_cores_per_host\x18\x07 \x01(\x05\x12\x16\n\x0eincluded_cores\x18\x08 \x03(\x05\x12\x12\n\nsignatures\x18\t \x03(\t\x1a\xef\x01\n\x0cTracingStats\x12\x15\n\rtotal_tensors\x18\x01 \x01(\x05\x12\x16\n\x0etraced_tensors\x18\x02 \x01(\x05\x12_\n\x13traced_tensor_types\x18\x03 \x03(\x0b\x32\x42.tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry\x12\x15\n\radded_tensors\x18\x04 \x01(\x05\x1a\x38\n\x16TracedTensorTypesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\xa9\x02\n\x0fTracedTensorDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x61\x63he_index\x18\x02 \x01(\x05\x12\x18\n\x10trace_point_name\x18\x03 \x01(\t\x12\x11\n\tis_traced\x18\x04 \x01(\x08\x12\x13\n\x0b\x65xplanation\x18\x05 \x01(\t\x12K\n\rop_stack_info\x18\x06 \x01(\x0b\x32\x34.tensorflow.TensorTracerReport.TracedTensorDef.Stack\x1a\x64\n\x05Stack\x12\x16\n\x0estack_fn_names\x18\x01 \x03(\t\x12\x13\n\x0bstack_lines\x18\x02 \x03(\t\x12\x17\n\x0fstack_filenames\x18\x03 \x03(\t\x12\x15\n\rstack_linenos\x18\x04 \x03(\x05\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_graph__pb2.DESCRIPTOR,])




_TENSORTRACERREPORT_TENSORDEFENTRY = _descriptor.Descriptor(
  name='TensordefEntry',
  full_name='tensorflow.TensorTracerReport.TensordefEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.TensorTracerReport.TensordefEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.TensorTracerReport.TensordefEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=460,
  serialized_end=556,
)

_TENSORTRACERREPORT_TENSORTRACERCONFIG = _descriptor.Descriptor(
  name='TensorTracerConfig',
  full_name='tensorflow.TensorTracerReport.TensorTracerConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='version', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.version', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.device', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_mode', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.trace_mode', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_cores', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.num_cores', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_hosts', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.num_hosts', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submode', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.submode', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_cores_per_host', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.num_cores_per_host', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='included_cores', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.included_cores', index=7,
      number=8, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signatures', full_name='tensorflow.TensorTracerReport.TensorTracerConfig.signatures', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=559,
  serialized_end=759,
)

_TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY = _descriptor.Descriptor(
  name='TracedTensorTypesEntry',
  full_name='tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry.value', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=945,
  serialized_end=1001,
)

_TENSORTRACERREPORT_TRACINGSTATS = _descriptor.Descriptor(
  name='TracingStats',
  full_name='tensorflow.TensorTracerReport.TracingStats',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total_tensors', full_name='tensorflow.TensorTracerReport.TracingStats.total_tensors', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='traced_tensors', full_name='tensorflow.TensorTracerReport.TracingStats.traced_tensors', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='traced_tensor_types', full_name='tensorflow.TensorTracerReport.TracingStats.traced_tensor_types', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='added_tensors', full_name='tensorflow.TensorTracerReport.TracingStats.added_tensors', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=762,
  serialized_end=1001,
)

_TENSORTRACERREPORT_TRACEDTENSORDEF_STACK = _descriptor.Descriptor(
  name='Stack',
  full_name='tensorflow.TensorTracerReport.TracedTensorDef.Stack',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stack_fn_names', full_name='tensorflow.TensorTracerReport.TracedTensorDef.Stack.stack_fn_names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stack_lines', full_name='tensorflow.TensorTracerReport.TracedTensorDef.Stack.stack_lines', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stack_filenames', full_name='tensorflow.TensorTracerReport.TracedTensorDef.Stack.stack_filenames', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stack_linenos', full_name='tensorflow.TensorTracerReport.TracedTensorDef.Stack.stack_linenos', index=3,
      number=4, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1201,
  serialized_end=1301,
)

_TENSORTRACERREPORT_TRACEDTENSORDEF = _descriptor.Descriptor(
  name='TracedTensorDef',
  full_name='tensorflow.TensorTracerReport.TracedTensorDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='tensorflow.TensorTracerReport.TracedTensorDef.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cache_index', full_name='tensorflow.TensorTracerReport.TracedTensorDef.cache_index', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_point_name', full_name='tensorflow.TensorTracerReport.TracedTensorDef.trace_point_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_traced', full_name='tensorflow.TensorTracerReport.TracedTensorDef.is_traced', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='explanation', full_name='tensorflow.TensorTracerReport.TracedTensorDef.explanation', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_stack_info', full_name='tensorflow.TensorTracerReport.TracedTensorDef.op_stack_info', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_TENSORTRACERREPORT_TRACEDTENSORDEF_STACK, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1004,
  serialized_end=1301,
)

_TENSORTRACERREPORT = _descriptor.Descriptor(
  name='TensorTracerReport',
  full_name='tensorflow.TensorTracerReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='config', full_name='tensorflow.TensorTracerReport.config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graphdef', full_name='tensorflow.TensorTracerReport.graphdef', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensordef', full_name='tensorflow.TensorTracerReport.tensordef', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fingerprint', full_name='tensorflow.TensorTracerReport.fingerprint', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='concrete_function_name', full_name='tensorflow.TensorTracerReport.concrete_function_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_common_frame_no', full_name='tensorflow.TensorTracerReport.last_common_frame_no', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outputs', full_name='tensorflow.TensorTracerReport.outputs', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tracing_stats', full_name='tensorflow.TensorTracerReport.tracing_stats', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_TENSORTRACERREPORT_TENSORDEFENTRY, _TENSORTRACERREPORT_TENSORTRACERCONFIG, _TENSORTRACERREPORT_TRACINGSTATS, _TENSORTRACERREPORT_TRACEDTENSORDEF, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=1301,
)

_TENSORTRACERREPORT_TENSORDEFENTRY.fields_by_name['value'].message_type = _TENSORTRACERREPORT_TRACEDTENSORDEF
_TENSORTRACERREPORT_TENSORDEFENTRY.containing_type = _TENSORTRACERREPORT
_TENSORTRACERREPORT_TENSORTRACERCONFIG.containing_type = _TENSORTRACERREPORT
_TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY.containing_type = _TENSORTRACERREPORT_TRACINGSTATS
_TENSORTRACERREPORT_TRACINGSTATS.fields_by_name['traced_tensor_types'].message_type = _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY
_TENSORTRACERREPORT_TRACINGSTATS.containing_type = _TENSORTRACERREPORT
_TENSORTRACERREPORT_TRACEDTENSORDEF_STACK.containing_type = _TENSORTRACERREPORT_TRACEDTENSORDEF
_TENSORTRACERREPORT_TRACEDTENSORDEF.fields_by_name['op_stack_info'].message_type = _TENSORTRACERREPORT_TRACEDTENSORDEF_STACK
_TENSORTRACERREPORT_TRACEDTENSORDEF.containing_type = _TENSORTRACERREPORT
_TENSORTRACERREPORT.fields_by_name['config'].message_type = _TENSORTRACERREPORT_TENSORTRACERCONFIG
_TENSORTRACERREPORT.fields_by_name['graphdef'].message_type = tensorflow_dot_core_dot_framework_dot_graph__pb2._GRAPHDEF
_TENSORTRACERREPORT.fields_by_name['tensordef'].message_type = _TENSORTRACERREPORT_TENSORDEFENTRY
_TENSORTRACERREPORT.fields_by_name['tracing_stats'].message_type = _TENSORTRACERREPORT_TRACINGSTATS
DESCRIPTOR.message_types_by_name['TensorTracerReport'] = _TENSORTRACERREPORT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TensorTracerReport = _reflection.GeneratedProtocolMessageType('TensorTracerReport', (_message.Message,), {

  'TensordefEntry' : _reflection.GeneratedProtocolMessageType('TensordefEntry', (_message.Message,), {
    'DESCRIPTOR' : _TENSORTRACERREPORT_TENSORDEFENTRY,
    '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TensordefEntry)
    })
  ,

  'TensorTracerConfig' : _reflection.GeneratedProtocolMessageType('TensorTracerConfig', (_message.Message,), {
    'DESCRIPTOR' : _TENSORTRACERREPORT_TENSORTRACERCONFIG,
    '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TensorTracerConfig)
    })
  ,

  'TracingStats' : _reflection.GeneratedProtocolMessageType('TracingStats', (_message.Message,), {

    'TracedTensorTypesEntry' : _reflection.GeneratedProtocolMessageType('TracedTensorTypesEntry', (_message.Message,), {
      'DESCRIPTOR' : _TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY,
      '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
      # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TracingStats.TracedTensorTypesEntry)
      })
    ,
    'DESCRIPTOR' : _TENSORTRACERREPORT_TRACINGSTATS,
    '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TracingStats)
    })
  ,

  'TracedTensorDef' : _reflection.GeneratedProtocolMessageType('TracedTensorDef', (_message.Message,), {

    'Stack' : _reflection.GeneratedProtocolMessageType('Stack', (_message.Message,), {
      'DESCRIPTOR' : _TENSORTRACERREPORT_TRACEDTENSORDEF_STACK,
      '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
      # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TracedTensorDef.Stack)
      })
    ,
    'DESCRIPTOR' : _TENSORTRACERREPORT_TRACEDTENSORDEF,
    '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
    # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport.TracedTensorDef)
    })
  ,
  'DESCRIPTOR' : _TENSORTRACERREPORT,
  '__module__' : 'tensorflow.python.tpu.tensor_tracer_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.TensorTracerReport)
  })
_sym_db.RegisterMessage(TensorTracerReport)
_sym_db.RegisterMessage(TensorTracerReport.TensordefEntry)
_sym_db.RegisterMessage(TensorTracerReport.TensorTracerConfig)
_sym_db.RegisterMessage(TensorTracerReport.TracingStats)
_sym_db.RegisterMessage(TensorTracerReport.TracingStats.TracedTensorTypesEntry)
_sym_db.RegisterMessage(TensorTracerReport.TracedTensorDef)
_sym_db.RegisterMessage(TensorTracerReport.TracedTensorDef.Stack)


_TENSORTRACERREPORT_TENSORDEFENTRY._options = None
_TENSORTRACERREPORT_TRACINGSTATS_TRACEDTENSORTYPESENTRY._options = None
# @@protoc_insertion_point(module_scope)
