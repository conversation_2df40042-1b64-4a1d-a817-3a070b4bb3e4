# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Functions for Python 2 vs. 3 compatibility that are private to TensorFlow."""

from tensorflow.python.util.compat import as_str_any


def path_to_str(path):
  """Returns the file system path representation of a `PathLike` object,
  else as it is.

  Args:
    path: An object that can be converted to path representation.

  Returns:
    A `str` object.
  """
  if hasattr(path, "__fspath__"):
    path = as_str_any(path.__fspath__())
  return path
