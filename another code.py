import numpy as np
from tensorflow.keras.preprocessing import image
from tensorflow.keras.models import model_from_json
import cv2

json_file = open('model1.json', 'r')
loaded_model_json = json_file.read()
json_file.close()
loaded_model = model_from_json(loaded_model_json)
# load weights into new model
loaded_model.load_weights("model1.h5")
print("Loaded model from disk")


label=["Apple___Apple_scab","Apple___Black_rot","Apple___Cedar_apple_rust","Apple___Healthy",
       "Grape___Black_rot","Grape___Esca_(Black_Measles)","Grape___Healthy",
       "Grape___Leaf_blight_(Isariopsis_Leaf_Spot)",
       "Potato___Early_blight","Potato___Healthy","Tomato___Bacterial_spot",
       "Tomato___Healthy","Tomato___Leaf_Mold",
       "Tomato___Tomato_Yellow_Leaf_Curl_Virus","Tomato___Tomato_mosaic_virus"]

test_image = image.load_img('Training_purpose/TYLCV1.PNG', target_size = (128, 128))
test_image = image.img_to_array(test_image)
test_image = np.expand_dims(test_image, axis = 0)
result = loaded_model.predict(test_image)
print("Prediction result:",result)
print("Prediction shape:",result.shape)
print("Prediction index:",result.argmax())

fresult=np.max(result)
predicted_index=result.argmax()
if predicted_index < len(label):
    label2 = label[predicted_index]
    print("Predicted class:",label2)
else:
    print("Predicted index is out of range. Check your model's output shape.")









