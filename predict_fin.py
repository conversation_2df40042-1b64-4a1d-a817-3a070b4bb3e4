# -*- coding: utf-8 -*-
# MLP for Pima Indians Dataset Serialize to JSON and HDF5
import numpy as np
from tensorflow.keras.preprocessing import image
from tensorflow.keras.models import model_from_json
import cv2

json_file = open('model1.json', 'r')
loaded_model_json = json_file.read()
json_file.close()
loaded_model = model_from_json(loaded_model_json)
# load weights into new model
loaded_model.load_weights("model1.h5")
print("Loaded model from disk")


label=["Apple___Apple_scab","Apple___Black_rot","Apple___Cedar_apple_rust","Apple___Healthy",
       "Corn_(maize)___Cercospora_leaf_spot Gray_leaf_spot","Corn_(maize)___Common_rust_",
       "Corn_(maize)___Healthy","Corn_(maize)___Northern_Leaf_Blight","Grape___Black_rot",
       "Grape___Esca_(Black_Measles)","Grape___Healthy","Grape___Leaf_blight_(Isariopsis_Leaf_Spot)",
       "Potato___Early_blight","Potato___Healthy","Potato___Late_blight","Tomato___Bacterial_spot",
       "Tomato___Early_blight","Tomato___Healthy","Tomato___Late_blight","Tomato___Leaf_Mold",
       "Tomato___Septoria_leaf_spot","Tomato___Spider_mites Two-spotted_spider_mite","Tomato___Target_Spot",
       "Tomato___Tomato_Yellow_Leaf_Curl_Virus","Tomato___Tomato_mosaic_virus"]

test_image = image.load_img('Training_purpose/TYLCV1.PNG', target_size = (128, 128))
test_image = image.img_to_array(test_image)
test_image = np.expand_dims(test_image, axis = 0)
result = loaded_model.predict(test_image)
print("Prediction result shape:", result.shape)
print("Result:", result)
print("Number of classes in model:", result.shape[1])
print("Number of labels available:", len(label))

fresult=np.max(result)
predicted_index = result.argmax()
print("Predicted index:", predicted_index)

# Add bounds checking to prevent IndexError
if predicted_index < len(label):
    label2 = label[predicted_index]
    print("Predicted class:", label2)
else:
    print(f"Error: Predicted index {predicted_index} is out of range. Available labels: 0-{len(label)-1}")
    print("Please check if your model and label list match.")






















'''
a=np.round(result[0][0])
b=np.round(result[0][1])
c=np.round(result[0][2])
d=np.round(result[0][3])

print(a)
print(b)
print(c)
print(d)
'''

'''
label=["cats","dogs","horse","rose"]
test_image=cv2.imread("18.jpg")

tf=test_image.reshape(-1,test_image.shape[0],test_image.shape[1],test_image.shape[2])

prediction=loaded_model.predict_classes(tf)
fresult=np.max(prediction)
label2=label[prediction.argmax()]
print(label2)'''

'''
if result[0][0] == 1:
    prediction = 'dog'
    print(prediction)
elif result[0][1] == 1:
    prediction = 'cat'
    print(prediction)
elif result[0][2]== 1:
    prediction = 'Horse'
    print(prediction)
elif result[0][3] == 1:
    prediction = 'rose'
    print(prediction)
'''


